"""
通用的case数据库操作模块 - 遵循DRY原则

此模块提供通用的good/bad case数据库操作功能，避免代码重复。
"""

import json
from datetime import datetime
from src.utils.logger import logger
from src.db.connection import execute_db_query
from src.db.database_enum import Database


def mark_chat_history_as_case(
    chat_history_id: int,
    case_type: str,  # 'good' or 'bad'
    is_marked: bool = True,
    marked_by: str = None,
    feedback_tags: list = None,
    custom_feedback: str = None
) -> bool:
    """
    通用的chat_history级别case标记数据库操作函数

    Args:
        chat_history_id (int): chat history记录的ID
        case_type (str): case类型，'good' 或 'bad'
        is_marked (bool): 是否标记为case
        marked_by (str): 标记用户名
        feedback_tags (list): 反馈标签
        custom_feedback (str): 自定义反馈

    Returns:
        bool: 操作是否成功
    """
    # 参数验证
    if case_type not in ['good', 'bad']:
        logger.error(f"Invalid case_type: {case_type}. Must be 'good' or 'bad'")
        return False

    if not chat_history_id or not isinstance(chat_history_id, int) or chat_history_id <= 0:
        logger.warning(f"Valid chat history ID is required to mark a {case_type} case, got: {chat_history_id}")
        return False

    table_name = f"{case_type}_case"
    action = "mark" if is_marked else "unmark"

    # 添加递归深度保护
    import sys
    current_frame_count = len([frame for frame in sys._current_frames().values()])
    if current_frame_count > 100:  # 设置合理的递归深度限制
        logger.error(f"Potential recursion detected in mark_chat_history_as_case, frame count: {current_frame_count}")
        return False

    try:
        if is_marked:
            # 验证chat_history记录是否存在，并获取conversation_id
            # 使用更安全的查询方式，避免潜在的递归调用
            check_sql = "SELECT conversation_id FROM chat_history WHERE id = %s LIMIT 1"

            try:
                result = execute_db_query(check_sql, (chat_history_id,), fetch='one', database=Database.CHATBI)
            except Exception as db_error:
                logger.error(f"Database error when checking chat history {chat_history_id}: {str(db_error)}")
                return False

            if not result or not result.get('conversation_id'):
                logger.warning(f"Chat history with ID {chat_history_id} does not exist or has no conversation_id")
                return False

            conversation_id = result['conversation_id']

            # 验证conversation_id的有效性
            if not conversation_id or not isinstance(conversation_id, str):
                logger.warning(f"Invalid conversation_id for chat history {chat_history_id}: {conversation_id}")
                return False

            # 处理反馈标签序列化
            feedback_tags_json = None
            if feedback_tags and isinstance(feedback_tags, list):
                try:
                    feedback_tags_json = json.dumps(feedback_tags, ensure_ascii=False)
                except Exception as e:
                    logger.warning(f"Failed to serialize feedback tags {feedback_tags}: {e}")
                    feedback_tags_json = None

            feedback_submitted_at = datetime.now()

            # 构建SQL语句，避免f-string注入风险
            if case_type == 'bad':
                insert_sql = """
                    INSERT INTO bad_case (conversation_id, marked_by, feedback_tags, custom_feedback, feedback_submitted_at, chat_history_id)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                        marked_by = VALUES(marked_by),
                        feedback_tags = VALUES(feedback_tags),
                        custom_feedback = VALUES(custom_feedback),
                        feedback_submitted_at = VALUES(feedback_submitted_at),
                        repair_status = 0,
                        updated_at = CURRENT_TIMESTAMP
                """
            else:  # good_case
                insert_sql = """
                    INSERT INTO good_case (conversation_id, marked_by, feedback_tags, custom_feedback, feedback_submitted_at, chat_history_id)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                        marked_by = VALUES(marked_by),
                        feedback_tags = VALUES(feedback_tags),
                        custom_feedback = VALUES(custom_feedback),
                        feedback_submitted_at = VALUES(feedback_submitted_at),
                        updated_at = CURRENT_TIMESTAMP
                """

            values = (conversation_id, marked_by, feedback_tags_json, custom_feedback, feedback_submitted_at, chat_history_id)

            try:
                execute_db_query(insert_sql, values, commit=True, database=Database.CHATBI)
                logger.info(f"Successfully marked chat history {chat_history_id} as {case_type} case")
                return True
            except Exception as db_error:
                logger.error(f"Database error when inserting {case_type} case for chat history {chat_history_id}: {str(db_error)}")
                return False

        else:
            # 取消标记 - 从case表中删除记录
            if case_type == 'bad':
                delete_sql = "DELETE FROM bad_case WHERE chat_history_id = %s"
            else:
                delete_sql = "DELETE FROM good_case WHERE chat_history_id = %s"

            try:
                affected_rows = execute_db_query(delete_sql, (chat_history_id,), fetch='count', commit=True, database=Database.CHATBI)

                if affected_rows > 0:
                    logger.info(f"Successfully unmarked chat history {chat_history_id} as {case_type} case")
                    return True
                else:
                    logger.warning(f"Chat history {chat_history_id} was not marked as {case_type} case")
                    return False
            except Exception as db_error:
                logger.error(f"Database error when deleting {case_type} case for chat history {chat_history_id}: {str(db_error)}")
                return False

    except Exception as e:
        logger.error(f"Unexpected error {action}ing chat history {chat_history_id} as {case_type} case: {str(e)}", exc_info=True)
        return False


def mark_conversation_as_case(
    conversation_id: str,
    case_type: str,  # 'good' or 'bad'
    is_marked: bool = True,
    marked_by: str = None,
    feedback_tags: list = None,
    custom_feedback: str = None,
    chat_history_id: int = None
) -> bool:
    """
    通用的conversation级别case标记数据库操作函数

    Args:
        conversation_id (str): 对话ID
        case_type (str): case类型，'good' 或 'bad'
        is_marked (bool): 是否标记为case
        marked_by (str): 标记用户名
        feedback_tags (list): 反馈标签
        custom_feedback (str): 自定义反馈
        chat_history_id (int): 可选的chat_history_id，用于向后兼容

    Returns:
        bool: 操作是否成功
    """
    # 参数验证
    if case_type not in ['good', 'bad']:
        logger.error(f"Invalid case_type: {case_type}. Must be 'good' or 'bad'")
        return False

    if not conversation_id or not isinstance(conversation_id, str):
        logger.warning(f"Valid conversation ID is required to mark a {case_type} case, got: {conversation_id}")
        return False

    action = "mark" if is_marked else "unmark"

    # 添加递归深度保护
    import sys
    current_frame_count = len([frame for frame in sys._current_frames().values()])
    if current_frame_count > 100:  # 设置合理的递归深度限制
        logger.error(f"Potential recursion detected in mark_conversation_as_case, frame count: {current_frame_count}")
        return False

    try:
        if is_marked:
            # 处理反馈标签序列化
            feedback_tags_json = None
            if feedback_tags and isinstance(feedback_tags, list):
                try:
                    feedback_tags_json = json.dumps(feedback_tags, ensure_ascii=False)
                except Exception as e:
                    logger.warning(f"Failed to serialize feedback tags {feedback_tags}: {e}")
                    feedback_tags_json = None

            feedback_submitted_at = datetime.now()

            # 构建SQL语句，避免f-string注入风险
            if case_type == 'bad':
                insert_sql = """
                    INSERT INTO bad_case (conversation_id, marked_by, feedback_tags, custom_feedback, feedback_submitted_at, chat_history_id)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                        marked_by = VALUES(marked_by),
                        feedback_tags = VALUES(feedback_tags),
                        custom_feedback = VALUES(custom_feedback),
                        feedback_submitted_at = VALUES(feedback_submitted_at),
                        chat_history_id = VALUES(chat_history_id),
                        repair_status = 0,
                        updated_at = CURRENT_TIMESTAMP
                """
            else:  # good_case
                insert_sql = """
                    INSERT INTO good_case (conversation_id, marked_by, feedback_tags, custom_feedback, feedback_submitted_at, chat_history_id)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                        marked_by = VALUES(marked_by),
                        feedback_tags = VALUES(feedback_tags),
                        custom_feedback = VALUES(custom_feedback),
                        feedback_submitted_at = VALUES(feedback_submitted_at),
                        chat_history_id = VALUES(chat_history_id),
                        updated_at = CURRENT_TIMESTAMP
                """

            values = (conversation_id, marked_by, feedback_tags_json, custom_feedback, feedback_submitted_at, chat_history_id)

            try:
                execute_db_query(insert_sql, values, commit=True, database=Database.CHATBI)

                feedback_info = ""
                if feedback_tags or custom_feedback:
                    feedback_info = f" with feedback (tags: {feedback_tags}, text: {bool(custom_feedback)})"
                logger.info(f"Successfully marked conversation {conversation_id} as {case_type} case by {marked_by}{feedback_info}")
                return True
            except Exception as db_error:
                logger.error(f"Database error when inserting {case_type} case for conversation {conversation_id}: {str(db_error)}")
                return False

        else:
            # 取消标记 - 从case表中删除记录
            if case_type == 'bad':
                delete_sql = "DELETE FROM bad_case WHERE conversation_id = %s"
            else:
                delete_sql = "DELETE FROM good_case WHERE conversation_id = %s"

            try:
                affected_rows = execute_db_query(delete_sql, (conversation_id,), fetch='count', commit=True, database=Database.CHATBI)
                logger.info(f"Successfully unmarked conversation {conversation_id} as {case_type} case, {affected_rows} records removed")
                return True
            except Exception as db_error:
                logger.error(f"Database error when deleting {case_type} case for conversation {conversation_id}: {str(db_error)}")
                return False

    except Exception as e:
        logger.error(f"Unexpected error {action}ing conversation {conversation_id} as {case_type} case: {str(e)}", exc_info=True)
        return False
