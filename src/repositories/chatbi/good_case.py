"""
Good case repository module.

This module provides database operations for managing good cases.
"""

import json
from datetime import datetime
from src.utils.logger import logger
from src.db.connection import execute_db_query
from mysql.connector import Error


def mark_conversation_as_good_case(conversation_id: str, is_good_case: bool = True, marked_by: str = None,
                                 feedback_tags: list = None, custom_feedback: str = None,
                                 chat_history_id: int = None) -> bool:
    """
    Mark or unmark a conversation as a good case using the good_case table.

    注意：此函数保持向后兼容，但推荐使用新的mark_chat_history_as_good_case函数

    Args:
        conversation_id (str): The ID of the conversation to mark
        is_good_case (bool, optional): Whether to mark as good case (True) or unmark (False). Defaults to True.
        marked_by (str, optional): The username of the person marking the good case. Defaults to None.
        feedback_tags (list, optional): List of selected feedback tags. Defaults to None.
        custom_feedback (str, optional): Custom feedback text from user. Defaults to None.
        chat_history_id (int, optional): The specific chat history ID to mark. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    # 使用通用的case数据库操作函数，遵循DRY原则
    from src.repositories.chatbi.case_repository import mark_conversation_as_case
    return mark_conversation_as_case(
        conversation_id=conversation_id,
        case_type='good',
        is_marked=is_good_case,
        marked_by=marked_by,
        feedback_tags=feedback_tags,
        custom_feedback=custom_feedback,
        chat_history_id=chat_history_id
    )


def get_conversation_good_case_status(conversation_id: str) -> bool:
    """
    Check if a conversation is marked as a good case using the good_case table.

    Args:
        conversation_id (str): The ID of the conversation to check

    Returns:
        bool: True if the conversation is marked as a good case, False otherwise
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to check good case status")
        return False

    # Query the good_case table
    sql = "SELECT id FROM good_case WHERE conversation_id = %s"
    params = [conversation_id]

    # Limit to one row since we just need to check if the record exists
    sql += " LIMIT 1"

    try:
        result = execute_db_query(sql, params, fetch='one')
        is_good_case = result is not None
        logger.debug(f"Conversation {conversation_id} good case status: {is_good_case}")
        return is_good_case

    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error checking good case status: {e}", exc_info=True)
        return False


def get_conversation_good_case_feedback(conversation_id: str) -> dict:
    """
    Get feedback data for a good case conversation.

    Args:
        conversation_id (str): The ID of the conversation to get feedback for

    Returns:
        dict: Feedback data including tags, custom text, and submission time, or None if not marked as good case
              If marked as good case but no feedback submitted, returns a dict with empty feedback data
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to get good case feedback")
        return None

    # 修改查询：移除 feedback_submitted_at IS NOT NULL 条件，以便获取所有标记为good case的记录
    sql = """
        SELECT feedback_tags, custom_feedback, feedback_submitted_at
        FROM good_case
        WHERE conversation_id = %s
        LIMIT 1
    """

    try:
        result = execute_db_query(sql, (conversation_id,), fetch='one')

        if not result:
            # 没有标记为good case
            return None

        feedback_data = {
            'feedback_tags': [],
            'custom_feedback': result.get('custom_feedback', ''),
            'feedback_submitted_at': result.get('feedback_submitted_at')
        }

        # 解析JSON格式的标签数据
        feedback_tags_json = result.get('feedback_tags')
        if feedback_tags_json:
            try:
                feedback_data['feedback_tags'] = json.loads(feedback_tags_json)
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse feedback tags JSON for conversation {conversation_id}: {e}")
                feedback_data['feedback_tags'] = []

        # 如果没有提交反馈，但已标记为good case，返回空的反馈数据结构
        # 这样前端就能知道已经标记为good case，但还没有填写反馈
        return feedback_data

    except Error as e:
        logger.error(f"Database error getting good case feedback for {conversation_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error getting good case feedback: {e}", exc_info=True)
        return None


def get_bulk_good_case_feedback(conversation_ids: list) -> dict:
    """
    批量获取多个对话的好案例反馈信息

    Args:
        conversation_ids (list): 对话ID列表

    Returns:
        dict: 以conversation_id为key，反馈数据为value的字典
    """
    if not conversation_ids:
        return {}

    # 移除重复的ID并过滤掉空值
    conversation_ids = list(set(filter(None, conversation_ids)))
    if not conversation_ids:
        return {}

    # 构建查询参数占位符
    placeholders = ','.join(['%s'] * len(conversation_ids))
    sql = f"""
        SELECT conversation_id, feedback_tags, custom_feedback, feedback_submitted_at
        FROM good_case
        WHERE conversation_id IN ({placeholders})
    """

    try:
        results = execute_db_query(sql, tuple(conversation_ids), fetch='all')
        
        feedback_map = {}
        for result in results:
            conversation_id = result.get('conversation_id')
            if not conversation_id:
                continue
                
            feedback_data = {
                'feedback_tags': [],
                'custom_feedback': result.get('custom_feedback', ''),
                'feedback_submitted_at': result.get('feedback_submitted_at')
            }

            # 解析JSON格式的标签数据
            feedback_tags_json = result.get('feedback_tags')
            if feedback_tags_json:
                try:
                    feedback_data['feedback_tags'] = json.loads(feedback_tags_json)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"Failed to parse feedback tags JSON for conversation {conversation_id}: {e}")
                    feedback_data['feedback_tags'] = []

            feedback_map[conversation_id] = feedback_data

        return feedback_map

    except Error as e:
        logger.error(f"Database error getting bulk good case feedback: {e}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error getting bulk good case feedback: {e}", exc_info=True)
        return {}


def mark_chat_history_as_good_case(chat_history_id: int, is_good_case: bool = True, marked_by: str = None,
                                 feedback_tags: list = None, custom_feedback: str = None) -> bool:
    """
    Mark or unmark a specific chat history message as a good case.

    Args:
        chat_history_id (int): The ID of the chat history message to mark
        is_good_case (bool, optional): Whether to mark as good case (True) or unmark (False). Defaults to True.
        marked_by (str, optional): The username of the person marking the good case. Defaults to None.
        feedback_tags (list, optional): List of selected feedback tags. Defaults to None.
        custom_feedback (str, optional): Custom feedback text from user. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    # 使用通用的case数据库操作函数，遵循DRY原则
    from src.repositories.chatbi.case_repository import mark_chat_history_as_case
    return mark_chat_history_as_case(
        chat_history_id=chat_history_id,
        case_type='good',
        is_marked=is_good_case,
        marked_by=marked_by,
        feedback_tags=feedback_tags,
        custom_feedback=custom_feedback
    )