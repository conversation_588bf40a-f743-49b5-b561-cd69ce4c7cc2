"""
通用的case标记服务模块 - 遵循DRY原则

此模块提供通用的good/bad case标记功能，避免代码重复。
"""

import concurrent.futures
from src.utils.logger import logger

# 创建线程池用于异步通知
_NOTIFICATION_EXECUTOR = concurrent.futures.ThreadPoolExecutor(max_workers=2, thread_name_prefix="CaseNotification")


def mark_case_by_chat_history_id(
    chat_history_id: int, 
    case_type: str,  # 'good' or 'bad'
    is_marked: bool = True, 
    user_name: str = None,
    feedback_tags: list = None, 
    custom_feedback: str = None
) -> bool:
    """
    通用的chat_history级别case标记函数
    
    Args:
        chat_history_id (int): chat history记录的ID
        case_type (str): case类型，'good' 或 'bad'
        is_marked (bool): 是否标记为case
        user_name (str): 标记用户名
        feedback_tags (list): 反馈标签
        custom_feedback (str): 自定义反馈
        
    Returns:
        bool: 操作是否成功
    """
    if case_type not in ['good', 'bad']:
        logger.error(f"Invalid case_type: {case_type}. Must be 'good' or 'bad'")
        return False
    
    action = "marked" if is_marked else "unmarked"
    feedback_info = ""
    if feedback_tags or custom_feedback:
        feedback_info = f" with feedback (tags: {feedback_tags}, text: {bool(custom_feedback)})"
    logger.info(f"User {action} chat history {chat_history_id} as {case_type} case, user_name: {user_name}{feedback_info}")
    
    # 根据case类型导入相应的数据库函数
    if case_type == 'good':
        from src.repositories.chatbi.good_case import mark_chat_history_as_good_case
        db_func = mark_chat_history_as_good_case
        from src.services.chatbot.good_case_service import _send_good_case_notification_for_chat_history_async
        notification_func = _send_good_case_notification_for_chat_history_async
    else:  # bad
        from src.repositories.chatbi.bad_case import mark_chat_history_as_bad_case
        db_func = mark_chat_history_as_bad_case
        from src.services.chatbot.bad_case_service import _send_bad_case_notification_for_chat_history_async
        notification_func = _send_bad_case_notification_for_chat_history_async
    
    # 调用数据库层函数
    success = db_func(chat_history_id, is_marked, marked_by=user_name,
                     feedback_tags=feedback_tags, custom_feedback=custom_feedback)
    
    # 如果标记成功，发送通知
    if success:
        # 异步提交通知发送任务到线程池，不等待结果
        _NOTIFICATION_EXECUTOR.submit(
            notification_func,
            chat_history_id,
            is_marked,
            user_name
        )
        logger.info(f"已异步提交{case_type.title()} Case飞书通知发送任务，chat_history_id: {chat_history_id}")
    
    return success


def mark_case_by_conversation_id(
    conversation_id: str, 
    case_type: str,  # 'good' or 'bad'
    is_marked: bool = True, 
    user_name: str = None,
    feedback_tags: list = None, 
    custom_feedback: str = None
) -> bool:
    """
    通用的conversation级别case标记函数
    
    Args:
        conversation_id (str): 对话ID
        case_type (str): case类型，'good' 或 'bad'
        is_marked (bool): 是否标记为case
        user_name (str): 标记用户名
        feedback_tags (list): 反馈标签
        custom_feedback (str): 自定义反馈
        
    Returns:
        bool: 操作是否成功
    """
    if case_type not in ['good', 'bad']:
        logger.error(f"Invalid case_type: {case_type}. Must be 'good' or 'bad'")
        return False
    
    action = "marked" if is_marked else "unmarked"
    feedback_info = ""
    if feedback_tags or custom_feedback:
        feedback_info = f" with feedback (tags: {feedback_tags}, text: {bool(custom_feedback)})"
    logger.info(f"User {action} conversation {conversation_id} as {case_type} case, user_name: {user_name}{feedback_info}")
    
    # 根据case类型导入相应的数据库函数
    if case_type == 'good':
        from src.repositories.chatbi.good_case import mark_conversation_as_good_case
        return mark_conversation_as_good_case(conversation_id, is_marked, marked_by=user_name,
                                            feedback_tags=feedback_tags, custom_feedback=custom_feedback)
    else:  # bad
        from src.repositories.chatbi.bad_case import mark_conversation_as_bad_case
        return mark_conversation_as_bad_case(conversation_id, is_marked, marked_by=user_name,
                                           feedback_tags=feedback_tags, custom_feedback=custom_feedback)
