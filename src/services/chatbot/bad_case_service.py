"""
Chat quality service module.

This module provides business logic for managing chatbot quality and bad cases.
"""

import functools
import concurrent.futures
from typing import Optional

from src.utils.logger import logger
from src.repositories.chatbi.bad_case import (
    mark_conversation_as_bad_case,
    get_conversation_bad_case_status,
    update_bad_case_repair_status,
    mark_chat_history_as_bad_case,
)

# 创建专用线程池用于异步发送飞书通知
_NOTIFICATION_EXECUTOR = concurrent.futures.ThreadPoolExecutor(
    max_workers=5,  # 通知发送不需要太多线程
    thread_name_prefix="feishu_notification_worker"
)


def feishu_notification_decorator(func):
    """
    装饰器：在mark_bad_case执行成功后异步发送飞书机器人消息到群聊

    Args:
        func: 被装饰的函数

    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 执行原始函数
        result = func(*args, **kwargs)

        # 如果执行成功，则异步发送通知
        if result:
            # 获取参数值
            conversation_id = kwargs.get('conversation_id', args[0] if args else None)
            is_bad_case = kwargs.get('is_bad_case', args[1] if len(args) > 1 else True)
            user_name = kwargs.get('user_name', args[2] if len(args) > 2 else None)

            # 异步提交通知发送任务到线程池，不等待结果
            _NOTIFICATION_EXECUTOR.submit(
                _send_notification_async,
                conversation_id,
                is_bad_case,
                user_name
            )

            logger.info(f"已异步提交飞书通知发送任务，对话ID: {conversation_id}")

        return result

    return wrapper


def _send_notification_async(conversation_id: str, is_bad_case: bool, user_name: Optional[str]) -> None:
    """
    异步发送通知到飞书群聊（在线程池中执行）

    Args:
        conversation_id (str): 对话ID
        is_bad_case (bool): 是否为bad case
        user_name (Optional[str]): 标记用户名
    """
    try:
        # 获取所有用户消息内容并拼接
        from src.utils.conversation_utils import get_all_user_messages_content, get_last_assistant_message_content
        all_user_messages_content = get_all_user_messages_content(conversation_id)

        # 获取最后一条assistant回复内容
        last_assistant_content = get_last_assistant_message_content(conversation_id)

        # 获取对话所属用户信息
        from src.repositories.chatbi.history import check_conversation_owner
        conversation_owner = check_conversation_owner(conversation_id)
        conversation_user_name = None
        if conversation_owner:
            conversation_user_name = conversation_owner.get('username')

        from src.services.feishu.message_apis import send_bad_case_notification, send_bad_case_unmark_notification

        notification_func = send_bad_case_notification if is_bad_case else send_bad_case_unmark_notification
        action = "标记" if is_bad_case else "取消标记"

        # 传递标记用户和对话用户信息，以及最后一条assistant回复
        notification_sent = notification_func(
            conversation_id,
            user_name,  # 标记用户
            all_user_messages_content,
            conversation_user_name,  # 对话用户
            last_assistant_content  # 最后一条assistant回复
        )

        if notification_sent:
            logger.info(f"异步发送Bad Case{action}通知成功，对话ID: {conversation_id}")
        else:
            logger.warning(f"异步发送Bad Case{action}通知失败，对话ID: {conversation_id}")

    except Exception as e:
        action = "标记" if is_bad_case else "取消标记"
        logger.error(f"异步发送Bad Case{action}通知时发生异常: {str(e)}", exc_info=True)


def _send_bad_case_notification_for_chat_history_async(chat_history_id: int, is_bad_case: bool, user_name: str = None):
    """
    异步发送基于chat_history_id的Bad Case标记/取消标记通知

    Args:
        chat_history_id (int): chat history ID
        is_bad_case (bool): 是否为bad case
        user_name (Optional[str]): 标记用户名
    """
    try:
        # 获取chat_history记录的详细信息
        from src.repositories.chatbi.history import get_chat_history_by_id
        chat_history = get_chat_history_by_id(chat_history_id)

        if not chat_history:
            logger.warning(f"Chat history {chat_history_id} not found for notification")
            return

        conversation_id = chat_history.get('conversation_id')

        # 获取该chat_history_id对应的用户消息和assistant回复
        from src.utils.conversation_utils import get_chat_history_context_for_notification
        context = get_chat_history_context_for_notification(chat_history_id)

        if not context:
            logger.warning(f"No context found for chat history {chat_history_id}")
            return

        user_message_content = context.get('user_message')
        assistant_message_content = context.get('assistant_message')

        # 获取对话所属用户信息
        from src.repositories.chatbi.history import check_conversation_owner
        conversation_owner = check_conversation_owner(conversation_id)
        conversation_user_name = None
        if conversation_owner:
            conversation_user_name = conversation_owner.get('username')

        # 导入飞书通知函数（延迟导入避免循环依赖）
        from src.services.feishu.message_apis import send_bad_case_notification, send_bad_case_unmark_notification

        notification_func = send_bad_case_notification if is_bad_case else send_bad_case_unmark_notification
        action = "标记" if is_bad_case else "取消标记"

        # 发送通知，传递chat_history_id相关的消息内容
        notification_sent = notification_func(
            conversation_id,
            user_name,  # 标记用户
            user_message_content,  # 用户消息内容
            conversation_user_name,  # 对话用户
            assistant_message_content,  # assistant回复内容
            chat_history_id=chat_history_id  # 传递chat_history_id用于通知显示
        )

        if notification_sent:
            logger.info(f"异步发送Bad Case{action}通知成功，chat_history_id: {chat_history_id}")
        else:
            logger.warning(f"异步发送Bad Case{action}通知失败，chat_history_id: {chat_history_id}")

    except Exception as e:
        action = "标记" if is_bad_case else "取消标记"
        logger.error(f"异步发送Bad Case{action}通知时发生异常: {str(e)}", exc_info=True)

def _send_repair_notification_async(conversation_id: str, bad_case_info: dict, repaired_by: str, repair_status: int) -> None:
    """
    异步发送 Bad Case 处理通知 (P2P)

    Args:
        conversation_id (str): 对话ID
        bad_case_info (dict): Bad Case 的信息，包含标记人和所有者的open_id
        repaired_by (str): 修复者的名字
        repair_status (int): 修复状态 (1=已修复, 2=暂不修复)
    """
    try:
        if not bad_case_info:
            logger.warning(f"无法发送修复通知，因为缺少bad_case_info, conversation_id: {conversation_id}")
            return

        marked_by_open_id = bad_case_info.get('marked_by_open_id')
        owner_open_id = bad_case_info.get('owner_open_id')
        marked_by = bad_case_info.get('marked_by')

        # 构建 Dashboard 链接
        import os
        HOST_NAME = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")
        dashboard_link = f"{HOST_NAME}/dashboard?chat={conversation_id}"

        # 获取当前时间
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 获取修复说明
        repair_note = bad_case_info.get('repair_note', '') if bad_case_info else ''

        # 根据修复状态确定通知内容
        if repair_status == 1:
            status_text = "修复完成"
            status_emoji = "✅"
            status_color = "green"
            final_message = "您的 Bad Case 已经修复完成，感谢您的反馈！"
        elif repair_status == 2:
            status_text = "暂不修复"
            status_emoji = "⏸️"
            status_color = "orange"
            final_message = "经过评估，此 Bad Case 暂时不进行修复。如有疑问，请联系管理员。"
        else:
            status_text = "处理完成"
            status_emoji = "ℹ️"
            status_color = "blue"
            final_message = "您的 Bad Case 已经处理完成。"

        # 构建 Markdown 内容
        markdown_content = f"""**处理人员**: {repaired_by}
**标记人员**: {marked_by or '未知用户'}
**对话ID**: `{conversation_id}`
**处理时间**: {timestamp}
**处理结果**: {status_text}"""

        # 如果有修复说明，添加到内容中
        if repair_note:
            markdown_content += f"""
**处理说明**: {repair_note}"""

        markdown_content += f"""

**操作**: [📊 查看对话详情]({dashboard_link})

---
{final_message}"""

        # 构造飞书交互式卡片消息
        card_content = {
            "header": {
                "template": status_color,
                "title": {
                    "content": f"{status_emoji} Bad Case {status_text}通知",
                    "tag": "lark_md",
                },
            },
            "elements": [
                {
                    "tag": "markdown",
                    "content": markdown_content,
                }
            ],
        }

        # 使用集合避免重复发送
        recipient_ids = set()
        if marked_by_open_id:
            recipient_ids.add(marked_by_open_id)
        if owner_open_id:
            recipient_ids.add(owner_open_id)

        from src.services.feishu.message_core import send_feishu_message
        import json

        for open_id in recipient_ids:
            try:
                # 发送美化的卡片消息
                success = send_feishu_message(
                    chat_id=open_id,
                    content=json.dumps(card_content),
                    msg_type="interactive",
                    receive_id_type="open_id"
                )
                if success:
                    logger.info(f"成功发送修复通知卡片到 open_id: {open_id}")
                else:
                    logger.error(f"发送修复通知卡片到 open_id {open_id} 失败")
            except Exception as e:
                logger.error(f"发送修复通知失败 open_id: {open_id}, error: {e}")

    except Exception as e:
        logger.error(f"异步发送 Bad Case 修复通知时发生异常: {str(e)}", exc_info=True)


def repair_notification_decorator(func):
    """
    装饰器：在 update_repair_status 执行成功且状态为“已修复”后，异步发送P2P通知
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 执行原始函数
        success, bad_case_info = func(*args, **kwargs)

        # 检查是否需要发送通知（已修复=1 或 暂不修复=2）
        repair_status = kwargs.get('repair_status', args[1] if len(args) > 1 else None)
        if success and repair_status in [1, 2]:
            conversation_id = kwargs.get('conversation_id', args[0] if args else None)
            repaired_by = kwargs.get('repaired_by', '管理员')  # 默认修复者

            # 异步发送通知
            _NOTIFICATION_EXECUTOR.submit(
                _send_repair_notification_async,
                conversation_id,
                bad_case_info,
                repaired_by,
                repair_status
            )
            status_text = "修复" if repair_status == 1 else "处理"
            logger.info(f"已异步提交 Bad Case {status_text}通知任务，对话ID: {conversation_id}")

        return success, bad_case_info
    return wrapper


@feishu_notification_decorator
def mark_bad_case(conversation_id: str, is_bad_case: bool = True, user_name: str = None,
                 feedback_tags: list = None, custom_feedback: str = None) -> bool:
    """
    Mark or unmark a conversation as a bad case.

    Args:
        conversation_id (str): The ID of the conversation
        is_bad_case (bool, optional): Whether to mark as bad case (True) or unmark (False). Defaults to True.
        user_name (str, optional): The name of the user who marked the bad case. Used for notification. Defaults to None.
        feedback_tags (list, optional): List of selected feedback tags. Defaults to None.
        custom_feedback (str, optional): Custom feedback text from user. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    action = "marked" if is_bad_case else "unmarked"
    feedback_info = ""
    if feedback_tags or custom_feedback:
        feedback_info = f" with feedback (tags: {feedback_tags}, text: {bool(custom_feedback)})"
    logger.info(f"User {action} conversation {conversation_id} as bad case, user_name: {user_name}{feedback_info}")
    return mark_conversation_as_bad_case(conversation_id, is_bad_case, marked_by=user_name,
                                       feedback_tags=feedback_tags, custom_feedback=custom_feedback)


def mark_bad_case_by_chat_history_id(chat_history_id: int, is_bad_case: bool = True, user_name: str = None,
                                   feedback_tags: list = None, custom_feedback: str = None) -> bool:
    """
    Mark or unmark a specific chat history message as a bad case.

    Args:
        chat_history_id (int): The ID of the chat history message
        is_bad_case (bool, optional): Whether to mark as bad case (True) or unmark (False). Defaults to True.
        user_name (str, optional): The name of the user who marked the bad case. Used for notification. Defaults to None.
        feedback_tags (list, optional): List of selected feedback tags. Defaults to None.
        custom_feedback (str, optional): Custom feedback text from user. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    # 参数验证
    if not chat_history_id or not isinstance(chat_history_id, int) or chat_history_id <= 0:
        logger.error(f"Invalid chat_history_id: {chat_history_id}")
        return False

    action = "marked" if is_bad_case else "unmarked"
    feedback_info = ""
    if feedback_tags or custom_feedback:
        feedback_info = f" with feedback (tags: {feedback_tags}, text: {bool(custom_feedback)})"
    logger.info(f"User {action} chat history {chat_history_id} as bad case, user_name: {user_name}{feedback_info}")

    # 直接调用通用的case数据库操作函数，避免多层调用
    try:
        from src.repositories.chatbi.case_repository import mark_chat_history_as_case
        success = mark_chat_history_as_case(
            chat_history_id=chat_history_id,
            case_type='bad',
            is_marked=is_bad_case,
            marked_by=user_name,
            feedback_tags=feedback_tags,
            custom_feedback=custom_feedback
        )
    except Exception as e:
        logger.error(f"Error calling mark_chat_history_as_case for chat_history {chat_history_id}: {str(e)}", exc_info=True)
        return False

    # 如果标记成功，发送通知
    if success:
        try:
            # 异步提交通知发送任务到线程池，不等待结果
            _NOTIFICATION_EXECUTOR.submit(
                _send_bad_case_notification_for_chat_history_async,
                chat_history_id,
                is_bad_case,
                user_name
            )
            logger.info(f"已异步提交Bad Case飞书通知发送任务，chat_history_id: {chat_history_id}")
        except Exception as e:
            logger.warning(f"Failed to submit notification task for chat_history {chat_history_id}: {str(e)}")
            # 通知失败不影响主要功能，继续返回成功

    return success

def is_bad_case(conversation_id: str, username: Optional[str] = None, email: Optional[str] = None) -> bool:
    """
    Check if a conversation is marked as a bad case.

    Args:
        conversation_id (str): The ID of the conversation
        username (str, optional): The username to filter by. Defaults to None (kept for API compatibility).
        email (str, optional): The email to filter by. Defaults to None (kept for API compatibility).

    Returns:
        bool: True if the conversation is marked as a bad case, False otherwise
    """
    logger.info(f"Checking if conversation {conversation_id} is a bad case")
    # Note: username and email parameters are kept for API compatibility but not used
    # since the new bad_case table design doesn't require user filtering
    return get_conversation_bad_case_status(conversation_id)





@repair_notification_decorator
def update_repair_status(conversation_id: str, repair_status: int, repaired_by: Optional[str] = None, repair_note: Optional[str] = None) -> (bool, Optional[dict]):
    """
    Update the repair status of a bad case and return bad case info.
    The actual notification is sent via the decorator.

    Args:
        conversation_id (str): The ID of the conversation
        repair_status (int): The new repair status (0=未修复, 1=已修复, 2=暂不修复)
        repaired_by (str, optional): The name of the user who repaired the case. Defaults to None.
        repair_note (str, optional): The repair note explaining how the issue was fixed. Defaults to None.

    Returns:
        tuple[bool, Optional[dict]]: A tuple containing a boolean indicating success
                                     and a dictionary with bad case info if successful.
    """
    logger.info(f"Updating repair status for conversation {conversation_id} to {repair_status}")
    if repair_note:
        logger.info(f"Repair note: {repair_note}")
    success, bad_case_info = update_bad_case_repair_status(conversation_id, repair_status, repair_note)
    return success, bad_case_info
